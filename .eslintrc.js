const path = require('path');

module.exports = {
  root: true,
  parser: '@babel/eslint-parser',
  env: {
    browser: true,
    commonjs: true,
    es6: true,
    jest: true,
    node: true,
  },
  plugins: ['import', 'jsx-a11y', 'prettier', 'react', 'react-hooks', 'testcafe'],
  settings: {
    react: {
      version: 'detect',
    },
    'import/resolver': {
      node: {
        paths: [path.resolve(__dirname, 'src')],
        extensions: ['.js', '.jsx', '.ts', '.tsx'],
      },
      typescript: './tsconfig.json',
    },
  },
  extends: [
    'eslint:recommended',
    'plugin:react/recommended',
    'plugin:import/recommended',
    'plugin:jsx-a11y/recommended',
    'prettier',
    'plugin:testcafe/recommended',
  ],
  rules: {
    // These rules stop developers from using features that have security implications
    'no-eval': 'error',
    'no-implied-eval': 'error',
    'no-script-url': 'error',
    // These rules make it harder to use the idiosyncratic, poorly designed parts of javascript
    eqeqeq: ['error', 'smart'],
    'no-extend-native': 'error',
    'no-extra-label': 'error',
    'no-label-var': 'error',
    'no-labels': 'error',
    'no-loop-func': 'error',
    // These rules steer developers towards better string construction
    'no-multi-str': 'error',
    'no-octal-escape': 'error',
    'no-template-curly-in-string': 'error',
    // These rules stop developers from creating redundant code
    'no-array-constructor': 'error',
    'no-extra-bind': 'error',
    'no-lone-blocks': 'error',
    'no-new-func': 'error',
    'no-new-object': 'error',
    'no-new-wrappers': 'error',
    'no-self-compare': 'error',
    'no-unused-expressions': [
      'error',
      {
        allowShortCircuit: true,
        allowTernary: true,
        allowTaggedTemplates: true,
      },
    ],
    'no-useless-computed-key': 'error',
    'no-useless-concat': 'error',
    'no-useless-constructor': 'error',
    'no-useless-rename': 'error',
    strict: ['error', 'never'],
    // These rules compliment our ways of working and agreed standards
    'array-callback-return': 'error',
    'default-case': [
      'error',
      {
        commentPattern: '^no default$',
      },
    ],
    'no-console': 'error',
    'no-sequences': 'error',
    'no-throw-literal': 'error',
    'no-use-before-define': [
      'error',
      {
        functions: false,
        classes: false,
        variables: false,
      },
    ],
    'prettier/prettier': 'error',
    // This rule lets us restrict the use of problematic properties and is highly configurable
    'no-restricted-properties': [
      'error',
      {
        object: 'require',
        property: 'ensure',
        message: 'Please use import() instead. More info: https://facebook.github.io/create-react-app/docs/code-splitting',
      },
      {
        object: 'System',
        property: 'import',
        message: 'Please use import() instead. More info: https://facebook.github.io/create-react-app/docs/code-splitting',
      },
    ],
    'no-restricted-syntax': [
      'error',
      {
        selector: "JSXAttribute[name.name='flex'][value.value='1']",
        message: 'IE needs an explicit flex-basis, try "1 1 auto" instead. See: https://roland.codes/blog/ie-flex-collapse-bug/',
      },
      {
        selector: 'CallExpression[callee.property.name="error"] Property[key.name="eventName"][value.value!=/.*_failed/]',
        message: 'logger.error event name needs to end in "_failed"',
      },
    ],
    // Plugin Rules
    // ------------
    // https://github.com/benmosher/eslint-plugin-import/tree/master/docs/rules
    'import/first': 'error',
    'import/no-amd': 'error',
    'import/no-webpack-loader-syntax': 'error',
    // https://github.com/yannickcr/eslint-plugin-react/tree/master/docs/rules
    'react/display-name': 'off',
    'react/jsx-pascal-case': ['error', { allowNamespace: true }],
    'react/no-typos': 'error',
    'react/style-prop-object': 'error',
    'react/default-props-match-prop-types': 'error',
    // 'react/forbid-component-props': ['error', { forbid: ['className', 'style'] }],
    // https://github.com/facebook/react/tree/master/packages/eslint-plugin-react-hooks
    'react-hooks/rules-of-hooks': 'warn',
    'react-hooks/exhaustive-deps': 'warn',
    // https://github.com/evcohen/eslint-plugin-jsx-a11y/tree/master/docs/rules
    'jsx-a11y/anchor-is-valid': [
      'error',
      {
        aspects: ['noHref', 'invalidHref'],
      },
    ],
    'jsx-a11y/alt-text': [
      'error',
      {
        elements: ['img', 'object', 'area', 'input[type="image"]'],
        img: ['Image'],
        object: ['Object'],
        area: ['Area'],
        'input[type="image"]': ['InputImage'],
      },
    ],
  },
  overrides: [
    {
      files: ['*.test.js', '**/test-utils/**/*.js', '*.test.ts', '**/test-utils/**/*.tsx'],
      plugins: ['jest'],
      env: {
        jest: true,
      },
      globals: {
        flushPromises: true,
        flushPromisesAct: true,
      },
      rules: {
        '@typescript-eslint/no-var-requires': 'off',
        'no-import-assign': 'off',
        'no-console': 'off',
        'import/namespace': 'off',
        'react/display-name': 'off',
        'react/prop-types': 'off',
        'jest/no-disabled-tests': 'warn',
        'jest/no-focused-tests': 'error',
        'jest/no-identical-title': 'error',
        'jest/prefer-to-have-length': 'warn',
        'jest/valid-expect': 'error',
        'no-restricted-syntax': [
          'error',
          {
            selector: 'CallExpression[callee.property.name="toHaveStyle"]',
            message:
              'Avoid using toHaveStyle(). Consider using snapshot tests instead to capture the complete rendered output and detect unintended changes.',
          },
          {
            selector: 'CallExpression[callee.property.name="toHaveAttribute"]',
            message:
              'Avoid using toHaveAttribute(). Consider using snapshot tests instead to capture the complete rendered output and detect unintended changes.',
          },
          {
            selector: 'CallExpression[callee.object.name="window"][callee.property.name="getComputedStyle"]',
            message:
              'Avoid using window.getComputedStyle(). Consider using snapshot tests instead to capture the complete rendered output and detect unintended changes.',
          },
          {
            selector: 'CallExpression[callee.name="getComputedStyle"]',
            message:
              'Avoid using getComputedStyle(). Consider using snapshot tests instead to capture the complete rendered output and detect unintended changes.',
          },
        ],
        'no-unused-vars': [
          'warn',
          
            args: 'after-used',
            argsIgnorePattern: '^_',
          },
        ],
      },
    },
    {
      files: ['*.stories.js'],
      rules: {
        'react/display-name': 'off',
        'react/prop-types': 'off',
      },
    },
    {
      files: ['*.{ts,tsx}'],
      extends: ['plugin:@typescript-eslint/eslint-recommended', 'plugin:@typescript-eslint/recommended', 'plugin:import/typescript'],
      rules: {
        'import/named': 'off',
        'no-use-before-define': 'off',
        '@typescript-eslint/no-use-before-define': ['error'],
      },
    },
    {
      files: ['*.tsx'],
      rules: {
        // Disable explicit return types for React FC
        '@typescript-eslint/explicit-module-boundary-types': 'off',
      },
    },
  ],
};
