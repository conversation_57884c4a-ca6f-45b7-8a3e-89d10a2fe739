import React from 'react';
import styled from '@emotion/styled';
import { ExternalLink, Text } from '@qga/roo-ui/components';
import { QCOM_TRAVELPASS_BALANCE_URL } from 'config';
import { BOOKING_ERROR_CODES } from 'lib/enums/booking';

const UnderlinedExternalLink = styled(ExternalLink)`
  text-decoration: underline;
`;

const errorDefinitions = {
  payment_failed_credit_card_declined: {
    heading: 'Your credit card has been declined',
    description: 'Please check your card details or contact your financial institution.',
  },
  payment_failed_credit_card_expired: {
    heading: 'Your credit card has expired',
    description: 'Please check your card details or contact your financial institution.',
  },
  payment_failed_credit_card_insufficient_balance: {
    heading: 'We had a problem processing your payment due to insufficient funds',
    description: 'Please check your card details or contact your financial institution.',
  },
  payment_failed_qantas_group_credit_voucher_insufficient_balance: {
    heading: 'We had a problem processing your TravelPass due to insufficient funds',
    description: (
      <Text>
        Please check your <UnderlinedExternalLink href={QCOM_TRAVELPASS_BALANCE_URL}>Qantas TravelPass balance</UnderlinedExternalLink> and
        try again.
      </Text>
    ),
  },
  payment_failed_voucher_expired: {
    heading: 'Your voucher has expired',
    description: 'Please check your voucher details.',
  },
  payment_failed_voucher_min_spend_not_met: {
    heading: 'We had a problem processing your payment',
    description: 'That voucher code is valid. However, it can only be used on bookings greater than $150.',
  },
  [BOOKING_ERROR_CODES.PAYMENT_FAILED_POINTS_TOKEN_NOT_PRESENT]: {
    heading: 'Your session has expired',
    description: 'Please log in again to pay with Qantas Points or click continue to pay with Credit Card',
  },
};

export default errorDefinitions;
