import { createMockStore } from 'redux-logic-test';
import { logic } from 'store/search/searchLogic';
import { searchLocationAvailability } from 'lib/clients/searchLocationAvailability';
import { fetchSearchResults, fetchSearchResultsSuccess, setLoading, fetchSearchResultsFailure } from 'store/search/searchActions';
import { getSearchQuery } from 'store/search/searchSelectors';
import { LIST_SEARCH_LIMIT } from 'config';
import { parseError, captureErrorInSentry } from 'lib/errors';
import { getBrowser } from 'store/userEnvironment/userEnvironmentSelectors';
import { setPointsLevels } from 'store/pointsConversion/pointsConversionActions';

jest.mock('store/search/searchSelectors');
jest.mock('lib/clients/searchLocationAvailability');
jest.mock('lib/errors/captureErrorInSentry');
jest.mock('store/userEnvironment/userEnvironmentSelectors');

const initialState = {
  search: {},
};

const tierInstance = {};

const searchQuery = { location: 'Melbourne', checkIn: new Date('2018-12-06'), checkOut: new Date('2018-12-08'), minStarRating: 3 };
const searchResponse = {
  meta: {
    totalResults: 1,
    pointsTierInstances: { tierInstance },
  },
  results: [{ property: { name: 'Property Name' } }],
};

let store;
const browser = { type: 'desktop' };

describe('fetchSearchResultsLogic', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    getSearchQuery.mockReturnValue(searchQuery);
    getBrowser.mockReturnValue(browser);
    searchLocationAvailability.mockResolvedValue(searchResponse);
    store = createMockStore({ initialState, logic });
  });

  describe('with qhUserId', () => {
    const qhUserId = 'qhUserId';

    beforeEach(() => {
      store = createMockStore({
        initialState: {
          user: {
            qhUserId,
          },
        },
        logic,
      });
      store.dispatch(fetchSearchResults());
    });

    it('calls searchLocationAvailability with the correct params including the accessToken', () => {
      expect(searchLocationAvailability).toHaveBeenCalledWith(expect.objectContaining({ ...searchQuery, qhUserId }));
    });
  });

  describe('with an accessToken', () => {
    const accessToken = 'accessToken';
    const flightBookerToken = 'flightBookerToken';

    beforeEach(() => {
      store = createMockStore({
        initialState: {
          user: {
            authentication: {
              accessToken: accessToken,
            },
            flightBookerToken: 'flightBookerToken',
          },
        },
        logic,
      });
      store.dispatch(fetchSearchResults());
    });

    it('calls searchLocationAvailability with the correct params including the accessToken', () => {
      expect(searchLocationAvailability).toHaveBeenCalledWith(expect.objectContaining({ accessToken, ...searchQuery, flightBookerToken }));
    });
  });

  describe('without an accessToken', () => {
    beforeEach(() => {
      store.dispatch(fetchSearchResults());
    });

    it('calls searchLocationAvailability with the correct params', () => {
      expect(searchLocationAvailability).toHaveBeenCalledWith(expect.objectContaining(searchQuery));
    });

    it('calls searchLocationAvailability without accessToken', () => {
      expect(searchLocationAvailability).not.toHaveBeenCalledWith(expect.objectContaining({ accessToken: expect.anything() }));
    });
  });

  describe('with a successful search response', () => {
    beforeEach(() => {
      store.dispatch(fetchSearchResults());
    });

    it('dispatches the setLoading action', async () => {
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setLoading(true), setLoading(false)]));
    });

    it('dispatches the fetchSearchResultsSuccess action', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([fetchSearchResultsSuccess({ ...searchResponse })]));
    });

    it('sets the qffPointsTiers', async () => {
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setPointsLevels({ ...searchResponse.meta.pointsTierInstances.tierInstance })]));
    });
  });

  describe('with an unsuccessful search response', () => {
    const error = new Error();
    error.response = { status: 500 };

    beforeEach(() => {
      searchLocationAvailability.mockRejectedValue(error);
      store.dispatch(fetchSearchResults());
    });

    it('dispatches the setLoading action', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([setLoading(true), setLoading(false)]));
    });

    it('dispatches the fetchSearchResultsFailure action', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(store.actions).toEqual(expect.arrayContaining([fetchSearchResultsFailure(parseError(error))]));
    });

    it('does not log the error with sentry', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(captureErrorInSentry).not.toHaveBeenCalled();
    });
  });

  describe('with a general error', () => {
    const error = new Error();

    beforeEach(() => {
      getSearchQuery.mockImplementation(() => {
        throw error;
      });
      store.dispatch(fetchSearchResults());
    });

    it('logs the error with sentry', async () => {
      expect.assertions(1);
      await store.whenComplete();
      expect(captureErrorInSentry).toHaveBeenCalledWith(expect.any(Error));
    });
  });

  describe('with a limit', () => {
    beforeEach(async () => {
      store.dispatch(fetchSearchResults({ limit: 2000 }));
      await store.whenComplete();
    });

    it('passes limit to search', () => {
      expect(searchLocationAvailability).toHaveBeenCalledWith(expect.objectContaining({ limit: 2000 }));
    });
  });

  describe('without a limit', () => {
    beforeEach(async () => {
      store.dispatch(fetchSearchResults());
      await store.whenComplete();
    });

    it('passes default limit to search', () => {
      expect(searchLocationAvailability).toHaveBeenCalledWith(expect.objectContaining({ limit: LIST_SEARCH_LIMIT }));
    });
  });
});
