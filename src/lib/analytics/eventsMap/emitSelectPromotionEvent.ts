import { selectPromotion } from 'store/campaign/campaignActions';
import { EventType, createEvent } from '@qantasexperiences/analytics';

const emitSelectPromotion = ({ payload }) => {
  return createEvent({
    type: EventType.SELECT_PROMOTION,
    payload: {
      promotion: payload.promotion,
    },
  });
};

// eslint-disable-next-line
// @ts-ignore
export default { [selectPromotion]: emitSelectPromotion };
