import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { trackBooking } from 'store/booking/bookingActions';
import emitBookingConfirmationEvent from './emitBookingConfirmationEvent';
import { getPageFromState } from './helpers';
import { TRACKING_PREFIX } from 'config';

jest.mock('store/booking/bookingSelectors');
jest.mock('./helpers');

let store;

declare global {
  interface Window {
    dataLayer?: unknown[];
  }
}

const middleware = createMiddleware(emitBookingConfirmationEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware]);
const createStore = (initialState) => mockStore(initialState);

describe('#emitBookingConfirmationEvent', () => {
  const booking = {
    id: 'a7ac819c-3868-44aa-a55b-b021d4605f20',
    bookingTotal: {
      creditCard: { total: '100' },
      points: { totalPoints: '0.00' },
      voucher: { total: '0.00' },
      creditNote: { total: '0.00' },
      qantasGroupCreditVoucher: { total: '0.00' },
    },
    value: {
      amount: '100',
      currency: 'AUD',
    },
    reservation: {
      checkIn: '2023-10-20',
      checkOut: '2023-10-24',
      adults: 2,
      children: 1,
      infants: 0,
      propertyBookingReference: 'HD2Q3DHPJY',
      property: {
        id: '7567',
        name: 'Bowral Hotel',
        category: 'hotels',
        rating: '4.0',
        address: { suburb: 'Bowral', state: 'NSW', country: 'Australia' },
      },
      roomType: {
        name: 'Single Room',
        roomTypeFacilities: ['Free Wi-fi', 'Free Breakfast'],
      },
      offer: {
        name: 'Parking Included',
        charges: {
          total: { amount: '100', currency: 'AUD' },
          payableAtBooking: {
            total: { amount: '100', currency: 'AUD' },
            tax: { amount: '10', currency: 'AUD' },
            taxDisplayable: { amount: '10', currency: 'AUD' },
          },
          payableAtProperty: {
            total: { amount: '0', currency: 'AUD' },
            tax: { amount: '0', currency: 'AUD' },
          },
        },
        promotion: {
          name: 'Luxury Exclusive Offer',
          promotionCode: 'luxeExclusiveOffer',
        },
        pointsEarned: { qffPoints: { total: 130 } },
        pointsTierInstance: {
          id: 'c42a9dae-a298-4b3c-99e6-17b2bd33f643',
          family: 'd5fc7d01-38cb-4b30-a322-d21002fe7b00',
          name: 'VERSION11',
          levels: [
            {
              min: 0,
              max: 150,
              rate: '0.00824',
            },
            {
              min: 150,
              max: 400,
              rate: '0.00834',
            },
            {
              min: 400,
              max: 650,
              rate: '0.00848',
            },
            {
              min: 650,
              max: 900,
              rate: '0.00875',
            },
            {
              min: 900,
              max: null,
              rate: '0.00931',
            },
          ],
        },
      },
    },
  };
  const quote = {
    offer: {
      name: 'Parking Included',
      charges: {
        total: {
          amount: '100',
          currency: 'AUD',
        },
        totalBeforeDiscount: {
          amount: '100',
          currency: 'AUD',
        },
        payableAtBooking: {
          discount: {
            amount: '0',
            currency: 'AUD',
          },
          tax: {
            amount: '0',
            currency: 'AUD',
          },
        },
        payableAtProperty: {
          total: {
            amount: '0.00',
            currency: 'AUD',
          },
        },
      },
      pointsEarned: {
        qffPoints: { total: 504 },
      },
      promotion: null,
    },
    property: {
      id: '160352',
      rating: 4,
      name: 'Comfort Inn & Suites Burwood',
      category: 'hotels',
      address: {
        state: 'New South Wales',
        country: 'Australia',
      },
    },
    roomType: {
      name: 'Single Room',
      roomTypeFacilities: [
        'Microwave',
        'Towels provided',
        'WiFi (surcharge)',
        'Coffee/tea maker',
        'Hair dryer',
        'Phone',
        'LCD TV',
        'Rollaway/extra beds (surcharge)',
        'Non-Smoking',
        'Bedsheets provided',
        'In-room safe',
        'Cribs/infant beds (surcharge)',
        'Cable TV service',
        'Free toiletries',
        'Daily housekeeping',
        'Iron/ironing board',
        'Bathtub or shower',
        'Air conditioning',
        'Minibar',
        'Free weekday newspaper',
        'Desk',
        'Mini-fridge',
        'Room service (limited hours)',
        'Shower only',
      ],
    },
    stay: {
      adults: 2,
      children: 0,
      infants: 0,
      checkIn: '2024-05-29',
      checkOut: '2024-05-30',
    },
  };

  beforeEach(() => {
    jest.clearAllMocks();

    (getPageFromState as jest.Mock).mockReturnValue({ name: `${TRACKING_PREFIX} Booking Confirmation Page` });

    window.dataLayer = [];
    store = createStore({});
  });

  it('for ga4, responds when the booking is fetched successfully', () => {
    store.dispatch(trackBooking({ booking, quote, isRebooked: true }));
    expect(window.dataLayer).toStrictEqual([
      {
        event: 'purchase',
        event_data: {
          action: 'purchase',
          component_type: 'checkout',
        },
        ecommerce: {
          transaction_id: 'a7ac819c-3868-44aa-a55b-b021d4605f20',
          value: 100,
          tax: 10,
          shipping: 0,
          payment_type: 'credit card',
          total_cash_used: 100,
          total_points_earned: 130,
          total_points_used_percentage: 0,
          total_points_used: 0,
          total_points_value: 12136,
          voucher_value: 0,
          discount_value: 0,
          coupon: 'luxeExclusiveOffer',
          voucher_used: false,
          currency: 'AUD',
          items: [
            {
              item_id: '7567',
              item_name: 'Bowral Hotel',
              item_category: 'hotels',
              item_variant: 'Single Room',
              item_offer: 'Parking Included',
              index: 0,
              price: 25,
              original_price: 25,
              cash_used: 100,
              points_used: 0,
              has_offer: true,
              rating: 4,
              luxe: true,
              start_date: '2023-10-20',
              end_date: '2023-10-24',
              travellers_adult: 2,
              travellers_children: 1,
              travellers_infant: 0,
              number_of_nights: 4,
              number_of_rooms: 1,
              exclusive_offer: true,
              points_earned: 130,
              pay_in_points_percentage: 0,
              offer_type: 'Luxury Exclusive Offer',
              rebook: true,
              includes: 'Free Wi-fi, Free Breakfast',
              payment_type: 'credit card',
              location: 'NSW, Australia',
              international_or_domestic: 'Domestic',
            },
          ],
        },
      },
    ]);
  });
});
