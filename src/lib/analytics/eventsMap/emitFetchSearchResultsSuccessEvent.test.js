import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { fetchSearchResultsSuccess } from 'store/search/searchActions';
import emitFetchSearchResultsSuccessEvent from './emitFetchSearchResultsSuccessEvent';
import { getQueryParams } from 'store/router/routerSelectors';
import { getPageFromState } from './helpers';
import { TRACKING_PREFIX } from 'config';

jest.mock('store/router/routerSelectors');
jest.mock('./helpers');

let store;

const middleware = createMiddleware(emitFetchSearchResultsSuccessEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware]);
const createStore = (initialState) => mockStore(initialState);

describe('#emitFetchSearchResultsSuccessEvent', () => {
  let initialState;
  const queryParams = { location: 'Melbourne', checkIn: '2018-12-06', checkOut: '2018-12-12', adults: 1, children: 2, infants: 3 };
  const results = [{ property: { name: 'Property Name' } }];
  const meta = {
    counts: {
      filtered: 123,
      total: 456,
    },
  };

  beforeEach(() => {
    getQueryParams.mockReturnValue(queryParams);
    getPageFromState.mockReturnValue({ name: `${TRACKING_PREFIX} Search Page` });

    initialState = {
      router: {
        action: 'push',
        location: {
          pathname: '/search/list',
        },
      },
    };

    window.dataLayer = [];
    store = createStore(initialState);
    store.dispatch(fetchSearchResultsSuccess({ results, meta }));
  });

  it('responds when results are fetched successfully', () => {
    expect(window.dataLayer[0]).toStrictEqual({
      event: 'user_interactions',
      user_event_category: 'Successful Search',
      user_event_action: 'Melbourne',
      user_event_label: `${TRACKING_PREFIX} Search Page`,
      numAdults: queryParams.adults,
      numChildren: queryParams.children,
      numInfants: queryParams.infants,
      checkIn: queryParams.checkIn,
      checkOut: queryParams.checkOut,
      location: queryParams.location,
      availablePropertyCount: 123,
      totalPropertyCount: 456,
    });
  });
});
