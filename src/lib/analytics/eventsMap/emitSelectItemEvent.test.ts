import { createMiddleware } from 'redux-beacon';
import GoogleTagManager from '@redux-beacon/google-tag-manager';
import configureMockStore from 'redux-mock-store';
import { selectItem } from 'store/property/propertyActions';
import { getPageFromState } from './helpers';
import { TRACKING_PREFIX } from 'config';
import emitSelectItemEvent from './emitSelectItemEvent';

jest.mock('store/booking/bookingSelectors');
jest.mock('./helpers');

let store;

declare global {
  interface Window {
    dataLayer?: unknown[];
  }
}

const payload = {
  category: 'jetstar',
  location: 'Melbourne, VIC, Australia',
  payWith: 'cash',
  availableProperties: 368,
  query: { checkIn: new Date('2024-06-26'), checkOut: new Date('2024-06-27'), adults: 2, children: 0, infants: 0 },
  pointsConversion: {
    levels: [
      { min: 0, max: 150, rate: 0.00824 },
      { min: 150, max: 400, rate: 0.00834 },
      { min: 400, max: 650, rate: 0.00848 },
      { min: 650, max: 900, rate: 0.00875 },
      { min: 900, max: null, rate: 0.00931 },
    ],
    name: 'VERSION11',
  },
  listName: 'Hotels in Melbourne, VIC, Australia',
  type: 'list',
  property: {
    category: 'hotels',
    customerRatings: [
      {
        source: 'trip_advisor',
        ratingImageUrl: 'www.tripadvisor.com/img/cdsi/img2/ratings/traveler/4.5-15969-4.png',
        averageRating: 4.5,
        reviewCount: 1415,
      },
    ],
    address: {
      suburb: 'Melbourne',
      postcode: '3000',
      state: 'Victoria',
      countryCode: 'AU',
      streetAddress: ['19 Little Bourke Street'],
      country: 'Australia',
    },
    ratingType: 'SELF_RATED',
    availabilityUrl:
      'https://www.qantas.com/hotels/properties/17186?adults=2&children=0&infants=0&checkIn=2024-06-27&checkOut=2024-06-28&page=1&payWith=cash&searchType=list&sortBy=popularity&location=Melbourne%2C+VIC%2C+Australia&utm_source=availability&utm_medium=qantas-hotels&utm_campaign=direct',
    mainImage: {
      caption: 'Midtown One Bedroom Suite - Living Area',
      urlSmall: 'https://images-cdn.qantashotels.com/insecure/thumb/plain/media/b67d91e3-bb86-4cec-96b2-f36bb4384694.jpg',
      urlOriginal: 'https://images-cdn.qantashotels.com/insecure/large/plain/media/b67d91e3-bb86-4cec-96b2-f36bb4384694.jpg',
      urlMedium: 'https://images-cdn.qantashotels.com/insecure/desktop_search/plain/media/b67d91e3-bb86-4cec-96b2-f36bb4384694.jpg',
      urlLarge: 'https://images-cdn.qantashotels.com/insecure/xlarge/plain/media/b67d91e3-bb86-4cec-96b2-f36bb4384694.jpg',
    },
    name: 'Laneways by Ovolo',
    longitude: 144.97203064,
    hasDepositPayableOffer: false,
    promotionSashes: [],
    id: '17186',
    latitude: -37.*********,
    propertyFacilities: [
      'Luggage storage',
      'Reception (24 hour)',
      'Porter / Bell staff',
      'Non-smoking property',
      'Lifts',
      'WiFi (free)',
      'Airport transfers (surcharge)',
      'Guest laundry',
      'Dry cleaning / Laundry service',
      'Onsite parking (surcharge)',
      'Accessible facilities',
    ],
    rating: 4.5,
  },
  offer: {
    cancellationPolicy: {
      description:
        'This property imposes the following penalties to its customers that Qantas Hotels is required to pass on: Cancellations or changes made after 14:00 (GMT+10:00) on Jun 25, 2024 are subject to a 1 night room & tax penalty. If you fail to check-in for this reservation, or if you cancel or change this reservation after check-in, you may incur penalty charges at the discretion of the property of up to 100% of the booking value. If you redeem Qantas Points or a Qantas Voucher please refer to the relevant Important Information and Terms & Conditions on the checkout page.',
      cancellationWindows: [
        {
          startTime: '2024-06-25T14:00:00+10:00',
          endTime: '2024-06-27T14:00:00+10:00',
          currency: 'AUD',
          nights: '1',
          formattedBeforeDate: 'Tue 25 Jun, 2024',
        },
      ],
      isNonrefundable: false,
    },
    name: 'OTA Flexible Rate 48HR CXL – Room Only',
    charges: {
      payableAtProperty: { breakdown: [], total: { amount: '0.00', currency: 'AUD' } },
      totalDiscount: { amount: '0.00', currency: 'AUD' },
      payableAtBooking: {
        tax: { amount: '27.18', currency: 'AUD' },
        taxRecovery: { amount: '0.00', currency: 'AUD' },
        total: { amount: '299.00', currency: 'AUD' },
        baseRate: { amount: '271.82', currency: 'AUD' },
        taxDisplayable: { amount: '27.18', currency: 'AUD' },
      },
      total: { amount: '299.00', currency: 'AUD' },
      totalBeforeDiscount: { amount: '299.00', currency: 'AUD' },
    },
    depositPay: { depositPayable: false },
    type: 'standard',
    inclusions: [{ description: 'WiFi Included', name: 'WiFi Included', code: 'internet' }],
    pointsTierInstanceId: 'c42a9dae-a298-4b3c-99e6-17b2bd33f643',
    id: '3458908',
    pointsEarned: {
      maxQffEarnPpd: 3,
      qbrPoints: { total: 299 },
      maxQbrEarnPpd: 1,
      qffPoints: { qffPointsClub: 0, total: 897, bonus: 0, base: 897 },
      promotionMultiplier: 1,
      propertyPpd: 3,
    },
    promotion: { name: 'Featured' },
  },
  roomType: { name: 'Downtown Studio', id: '21862', maxOccupantCount: 2 },
};

const middleware = createMiddleware(emitSelectItemEvent, GoogleTagManager());
const mockStore = configureMockStore([middleware]);
const createStore = (initialState) => mockStore(initialState);

describe('#emitSelectItemEvent', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    (getPageFromState as jest.Mock).mockReturnValue({ name: `${TRACKING_PREFIX} Booking Page` });

    window.dataLayer = [];
    store = createStore({});
  });

  it('fires with the correct data', () => {
    store.dispatch(selectItem(payload));
    expect(window.dataLayer).toStrictEqual([
      {
        event: 'select_item',
        event_data: {
          action: 'click',
          component_type: 'item_list',
          search_term: 'Melbourne, VIC, Australia',
          search_type: 'list',
          search_category: 'jetstar',
          search_payment_toggle: 'cash',
          available_property_count: 368,
          start_date: '2024-06-26',
          end_date: '2024-06-27',
          travellers_adult: 2,
        },
        ecommerce: {
          currency: 'AUD',
          item_list_id: 'hotels_in_melbourne_vic_australia',
          item_list_name: 'Hotels in Melbourne, VIC, Australia',
          items: [
            {
              item_id: '17186',
              item_name: 'Laneways by Ovolo',
              item_category: 'hotels',
              item_variant: 'Downtown Studio',
              index: 0,
              quantity: 1,
              price: 299,
              points_value: 36070,
              has_offer: true,
            },
          ],
        },
      },
    ]);
  });
});
