const path = require('path');
// const withBundleAnalyzer = require('@next/bundle-analyzer');
const { withSentryConfig } = require('@sentry/nextjs');
const withPlugins = require('next-compose-plugins');
const withImages = require('next-images');
const withTM = require('next-transpile-modules');

const __DEV__ = process.env.ENVIRONMENT === 'development';

/** @type {import('next').NextConfig['webpack']} */
function webpack(config, { dev, webpack }) {
  config.resolve.modules = [
    // resolve with our modules first (this allows overriding node_modules)
    path.resolve(__dirname, 'src'),
    ...config.resolve.modules,
  ];
  config.resolve.fallback = {
    ...config.resolve.fallback,
    fs: false,
    net: false,
    tls: false,
    cwd: false,
  };
  config.plugins = [
    ...config.plugins,
    new webpack.DefinePlugin({
      __DEV__: dev,
    }),
  ];

  return config;
}

/** @type {import('next').NextConfig} */
module.exports = withPlugins(
  [
    withTM(['@qga/roo-ui', '@qga/components', 'react-masonry']),
    withImages,
    (nextConfig) =>
      withSentryConfig(nextConfig, {
        // https://github.com/getsentry/sentry-webpack-plugin#options
        dryRun: __DEV__,
        silent: __DEV__,
        deploy: {
          env: process.env.ENVIRONMENT,
        },
      }),
    // withBundleAnalyzer({
    //   enabled: !__DEV__,
    // }),
  ],
  {
    compiler: {
      styledComponents: true,
    },
    reactStrictMode: true,
    redirects: () => [
      {
        basePath: false,
        source: '/lw99vjLKICbBOi12sklcjpwkckr32voo023ck/lb-health-check',
        destination: '/hotels/lw99vjLKICbBOi12sklcjpwkckr32voo023ck/lb-health-check',
        permanent: false,
      },
      {
        basePath: false,
        source: '/hotels/deals',
        destination: '/hotels/deals/sydney',
        permanent: false,
      },
      {
        basePath: false,
        source: '/au/en/hotels/deals',
        destination: '/au/en/hotels/deals/sydney',
        permanent: false,
      },
      {
        basePath: false,
        source: '/au/en/hotels/australia/:path*',
        destination: '/au/en/hotels',
        permanent: false,
      },
    ],
    rewrites: () => {
      // when in local dev, proxy the API requests to the defined HOTELS_API_URL
      // this gets rid of the CORS issue in local dev
      return __DEV__
        ? [
            {
              source: '/api/ui/:path*',
              destination: `${process.env.NEXT_PUBLIC_HOTELS_API_HOST}${process.env.NEXT_PUBLIC_HOTELS_API_BASE_PATH}/:path*`, // Proxy to Backend
            },
            {
              source: '/split/:path*',
              destination: `${process.env.NEXT_PUBLIC_HOTELS_API_HOST}${process.env.NEXT_PUBLIC_HOTELS_API_BASE_PATH}/split/:path*`,
            },
          ]
        : [];
    },
    basePath: '/au/en/hotels',
    distDir: 'build',
    poweredByHeader: false,
    productionBrowserSourceMaps: true,
    images: {
      domains: ['cdn.sanity.io'],
      dangerouslyAllowSVG: true,
      contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
    },
    assetPrefix: process.env.ASSET_PREFIX,
    webpack,
    pageExtensions: ['page.tsx', 'page.ts', 'page.jsx', 'page.js'],
  },
);

// exporting so it can be used in Storybook
module.exports.webpack = webpack;
